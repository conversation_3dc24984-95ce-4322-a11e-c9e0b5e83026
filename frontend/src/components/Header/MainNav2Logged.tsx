'use client';

import React, { FC, useEffect, useState } from 'react';
import Logo from '@/components/Logo/Logo';
import MenuBar from '@/components/MenuBar/MenuBar';
import AvatarDropdown from './AvatarDropdown';
import SearchModal from './SearchModal';
import Navigation from '@/components/Navigation/Navigation';
import { IMenuResponse, IThemeOptions } from '@/contains/types';
import { checkUserLogged } from '@/lib/user.client';
import NavigationAdmin from '@/components/Navigation/NavigationAdmin';
import { IAdmin } from '@/contains/author';
import { NAVIGATION_ADMIN, NAVIGATION_EDITOR } from '@/data/navigation';
import { ValidateID } from '@/components/ValidateID';
import { useAdminNavigationStore } from '@/stores/useAdminNavigationStore';

export interface MainNav2LoggedProps {
  themeData: IThemeOptions | null;
  menuData: IMenuResponse | null;
}

const MainNav2Logged: FC<MainNav2LoggedProps> = ({ ...props }: MainNav2LoggedProps) => {
  const { themeData, menuData } = props;

  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const [adminId, setAdminId] = useState<number>(0);
  const [isEditor, setIsEditor] = useState<boolean>(false);
  const { postId, userId, setActiveId } = useAdminNavigationStore();
  useEffect(() => {
    checkUserLogged().then((res: {data: IAdmin | null; error: any; meta: any}) => {
      if (res.data?.id) {
        setActiveId('user', res.data.id);
      }
      if (res.data?.roles) {
        const adminRoles = new Set(['admin', 'super admin']);
        const hasAdminRole = res.data.roles.some(role =>
          adminRoles.has(role.toLowerCase())
        );
        setIsAdmin(hasAdminRole);
        setIsEditor(!hasAdminRole);
      }
      return res;
    });
  }, []);

  useEffect(() => {
    if (!!userId) {
      setAdminId(adminId);
    }
  }, [userId]);

  const renderContent = () => {
    return (
      <div className="h-20 flex justify-between">
        <div className="flex items-center lg:hidden flex-1">
          <MenuBar />
        </div>

        <div className="lg:flex-1 flex items-center">
          <Logo img={themeData?.logo.logo ?? ''} height={themeData?.logo.logo_height ?? 0} />
        </div>


        <div className="flex-2 flex items-center justify-end text-slate-700 dark:text-slate-100">
          <div className="hidden lg:block">
            <Navigation menuItem={menuData?.data} />
          </div>
          <SearchModal />
          {/*<NotifyDropdown />*/}
          <AvatarDropdown />
        </div>
      </div>
    );
  };

  return (
    <div className="nc-MainNav2Logged relative z-10 bg-white dark:bg-neutral-900 border-b border-slate-100 dark:border-slate-700">
      {!( isAdmin || isEditor ) && (
        <NavigationAdmin
          // menu={isAdmin ? NAVIGATION_ADMIN : isEditor ? NAVIGATION_EDITOR : []}
          menu={NAVIGATION_ADMIN}
          postId={postId}
        />
      )}
      <div className="container ">{renderContent()}</div>
      <ValidateID userId={adminId} />
    </div>
  );
};

export default MainNav2Logged;
